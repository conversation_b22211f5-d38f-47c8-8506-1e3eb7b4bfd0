class API::TeamOrdersController < ApplicationController

  before_action :fetch_team_order, only: [:generate_order_manifest]
  before_action :fetch_suburb, except: %i[become_team_admin_customer generate_order_manifest]

  def create
    team_admin = session_profile
    byebug
    meal_plan = team_admin.meal_plans.where(kind: 'individual', uuid: params[:teamMealUUID]).first
    order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

    if order_creator.success?
      redirect_url = team_order_path(order_creator.team_order)
      if order_creator.team_order.is_package_order?
        redirect_url = team_order_package_path(order_creator.team_order)
      end
      render json: { redirect_url: redirect_url }, status: :ok
    else
      render json: { errors: order_creator.errors }, status: :unprocessable_entity
    end
  end

  # method to add user to team admin
  def become_team_admin
    request_sender = TeamOrders::Emails::SendNewTeamAdminRequestEmail.new(customer: session_profile).call
    case
    when request_sender.success?
      render json: { success: true, message: 'Your request was successfully sent.' }
    else
      errors = ['There was an error and we couldn\'t send the request', 'Please try again!']
      render json: { errors: errors }, status: :unprocessable_entity
    end
  end

  def refresh_suppliers_list
    retrieve_selected_suppliers

    if @suburb.present?
      lister_options = [default_lister_options, supplier_list_params.to_h.symbolize_keys].inject(&:merge)
      suppliers = Suppliers::List.new(options: lister_options).call
      @suppliers_minimums = Suppliers::GetMinimums.new(suppliers: suppliers.to_a).call
      lister_options[:minimums] = @suppliers_minimums

      selected_team_supplier = @team_order.present? && @team_order.team_supplier_profiles.present? ? @team_order.team_supplier_profiles.first : nil
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: suppliers, session_profile: session_profile, filter_options: lister_options, selected_supplier: selected_team_supplier).call
      @suppliers = supplier_sorter.sorted_suppliers
      @favourite_team_supplier_ids = supplier_sorter.favourite_supplier_ids
      @custom_menu_supplier_ids = supplier_sorter.custom_menu_supplier_ids
      @rate_card_supplier_ids = supplier_sorter.rate_card_supplier_ids
      @markup_override_supplier_ids = supplier_sorter.markup_override_supplier_ids
    else
      @suppliers = SupplierProfile.none
      @suppliers_minimums = {}
    end
  end

  def retrieve_supplier_menu
    @supplier = SupplierProfile.where(slug: params[:slug]).first
    @suppliers_minimums = Suppliers::GetMinimums.new(suppliers: [@supplier], category_group: 'catering-services').call
    menu_options = {
      team_order_menu: true,
      is_admin: is_admin?
    }
    @supplier_menu = Suppliers::FetchMenu.new(supplier: @supplier, suburb: @suburb, menu_options: menu_options, profile: session_profile).call
  end

  def fetch_suppliers_availability
    @supplier_availability = Suppliers::GetAvailableSuppliers.new(supplier_ids: cutoff_params[:supplier_ids], delivery_date: cutoff_params[:delivery_date], suburb: @suburb).call
  end

  def fetch_supplier_cutoff_hours_remaining
    @supplier = SupplierProfile.where(id: cutoff_params[:supplier_id]).first
    @cutoff_hours_fetcher = Suppliers::GetCutoffHours.new(supplier: @supplier, delivery_at: cutoff_params[:delivery_date], suburb: @suburb).call
  end

  def generate_order_manifest
    begin
      generated_document = Documents::Generate::CustomerOrderDetails.new(order: @team_order, variation: 'team_order_manifest').call
    rescue
      generated_document = nil
    end

    if generated_document.present?
      respond_to do |format|
        format.json do
          render json: { name: generated_document.name, url: generated_document.url, version: generated_document.version }
        end
      end
    else
      respond_to do |format|
        format.json do
          render json: { errors: 'We could not generate the team order manifest' }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def default_lister_options
    {
      searchable: true,
      cumulative: true,
      team_suppliers: true,
      category_group: 'catering-services',
      suburb: @suburb,
    }
  end

  def team_order_params
    params.permit(:delivery_date, :supplier_id, :selected_menu_sections)
  end

  def supplier_list_params
    params.permit(:search_keywords, category: [], dietary: [], delivery: [], other: [])
  end

  def fetch_team_order
    @team_order = session_profile.present? ? session_profile.orders.where(id: params[:team_order_id]).first : nil
  end

  def fetch_suburb
    @suburb = Suburb.where(id: (params[:suburb_id] || cookies[:yordar_suburb_id])).first
  end

  def cutoff_params
    params.permit(:delivery_date, :supplier_id, supplier_ids: [])
  end

  def retrieve_selected_suppliers
    @team_order = params[:team_order_id].present? ? session_profile.orders.where(order_variant: %w[team_order recurring_team_order], id: params[:team_order_id]).first : nil
    @selected_order_suppliers = case
    when @team_order.present?
      @team_order.order_suppliers
    when params[:selected_suppliers].present?
      params[:selected_suppliers].map do |seleted_supplier|
        OrderSupplier.new({
          supplier_profile_id: seleted_supplier[:supplier_profile_id],
          selected_menu_sections: seleted_supplier[:selected_menu_sections]
        })
      end
    else
      []
    end
  end

end
