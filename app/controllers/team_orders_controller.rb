class TeamOrdersController < ApplicationController

  before_action :authenticate_user!
  before_action :ensure_customer
  before_action :ensure_team_admin, except: :index

  before_action :fetch_team_order, only: %i[show show_package edit update clone extend]
  before_action :check_is_event_cancel, only: %i[edit update]

  def index
    customer = session_profile
    if !customer.team_admin?
      render 'become_team_admin', layout: 'customer_profiles' and return
    end

    @needs_billing_details = customer.billing_details.blank?
    @has_meal_plans = customer.meal_plans.where(kind: 'individual', archived_at: nil).present?
    @meal_plan = customer.meal_plans.where(kind: 'individual', archived_at: nil, uuid: params[:mealUUID]).first if params[:mealUUID].present?

    respond_to do |format|
      format.html { render layout: 'customer_profiles' }
    end
  end

  def show
    @team_order_spends = Orders::GetSupplierSpends.new(order: @team_order).call
    if params[:print] == 'true'
      @print_friendly = 'true'
      render layout: '/layouts/print_friendly'
    else
      respond_to do |format|
        format.html do
          render layout: 'customer_profiles'
        end
        format.json
      end
    end
  end

  def show_package
    @package_order = @team_order
    if @package_order.present? && @package_order.is_package_order?
      scoped_to = params[:scoped_to] || (@package_order.is_recurring_team_order? ? 'recent_fortnight' : 'recent_month')
      scoped_time = params[:scoped_time].present? ? Time.zone.parse(params[:scoped_time]) : nil
      scoped_time ||= params[:show_recent].present? ? Time.zone.now : [@package_order.delivery_at, Time.zone.now].max
      @lister_options = {
        scoped_to: scoped_to,
        scoped_time: scoped_time,
      }
      @package_orders = TeamOrders::ListPackageOrders.new(team_order: @package_order, options: @lister_options).call
      respond_to do |format|
        format.html do
          render 'show_package', layout: 'customer_profiles'
        end
        format.json
      end
    else
      respond_to do |format|
        format.html do
          render 'team_orders/missing_order', locals: { is_package_order: true }
        end
        format.json { render json: { not_found: true }, status: 404 }
      end
    end
  end

  def new
    @team_order = TeamOrders::SetupForNewOrder.new(team_admin: session_profile, recurring_team_order: params[:recurring_team_order]).call
    @event_attendees = session_profile.event_attendees.where(active: true)
    render layout: 'customer_profiles'
  end

  def create
    if team_order_params[:delivery_dates].present? && team_order_params[:delivery_dates].size > 1
      order_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_params, team_admin: session_profile).call
    else
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: session_profile).call
    end
    if order_creator.success?
      if order_creator.team_order.is_package_order?
        redirect_to team_order_package_path(order_creator.team_order)
      else
        redirect_to team_order_path(order_creator.team_order)
      end
    else
      @team_order = order_creator.team_order
      render :new, layout: 'customer_profiles', success: 'Team order was not created.'
    end
  end

  def edit
    edit_access_checker = Orders::CheckEditAccess.new(order: @team_order, current_user: current_user, profile: session_profile).call
    if edit_access_checker.success?
      respond_to do |format|
        format.html { render layout: 'customer_profiles' }
        # format.json
      end
    else
      respond_to do |format|
        format.html do
          render 'team_orders/missing_order', layout: 'customer_profiles', locals: { errors: edit_access_checker.errors }
        end
        format.json { render json: { not_found: true }, status: 404 }
      end
    end
  end

  def update
    order_updater = TeamOrders::Update.new(team_order: @team_order, team_order_params: team_order_params, team_admin: session_profile).call
    if order_updater.success?
      redirect_to team_order_path(order_updater.team_order)
    else
      @team_order = order_updater.team_order
      render :edit, layout: 'customer_profiles', success: 'Team order was not updated.'
    end
  end

  def clone
    order_cloner = TeamOrders::Clone.new(team_order: @team_order).call
    @team_order = order_cloner.cloned_team_order
    render 'new', layout: 'customer_profiles'
  end

  def extend
    if !@team_order.is_package_order?
      flash[:warning] = 'Cannot extend a non-packaged team order'
      redirect_to team_order_path(@team_order) and return
    end
    @package_order = @team_order
    order_cloner = TeamOrders::Clone.new(team_order: @package_order, extension_week: params[:week_starting]).call
    @team_order = order_cloner.cloned_team_order
    render layout: 'customer_profiles'
  end

private

  def ensure_team_admin
    if session_profile.team_admin?
      @team_admin = session_profile
    else
      flash[:error] = 'You are not allowed to access this page, and need to request to be a team admin.'
      redirect_to customer_team_orders_path
    end
  end

  def fetch_team_order
    @team_order = session_profile.present? && session_profile.orders.where(order_variant: %w[team_order recurring_team_order], id: params[:id]).first
    if @team_order.blank?
      respond_to do |format|
        format.html do
          render 'team_orders/missing_order', layout: 'customer_profiles', locals: { is_package_order: params[:action] == 'show_package' } and return
        end
        format.json do
          render json: { not_found: true }, status: 404 and return
        end
      end
    end
  end

  def check_is_event_cancel
    if @team_order.blank? || (@team_order.status == 'cancelled' && !is_admin?)
      flash[:warning] = 'This event has already been canceled'
      redirect_to customer_team_orders_path
    end
  end

  def clean_up_session
    session.delete('edit_params') if session[:edit_params].present?
 		clean_up_session_orders
  end

  def team_order_params
    order_fields = %i[name order_variant number_of_people cpo_id department_identity credit_card_id invoice_individually whodunnit_id]
    contact_fields = %i[contact_name company_name phone]
    delivery_fields = %i[delivery_at delivery_suburb_id delivery_address_level delivery_address delivery_instruction delivery_dates delivery_suppliers]
    team_order_detail_fields = %i[attendee_pays cutoff_option budget hide_budget package_id]
    permitted_params = params.require(:order).permit(*order_fields, *contact_fields, *delivery_fields, :mode, attendee_ids: [], team_order_detail_attributes: [*team_order_detail_fields, levels: [names: []]])
    permitted_params[:delivery_dates] = JSON.parse(permitted_params[:delivery_dates]) if permitted_params[:delivery_dates].present?
    permitted_params[:delivery_suppliers] = JSON.parse(permitted_params[:delivery_suppliers]) if permitted_params[:delivery_suppliers].present?
    permitted_params
  end

end
