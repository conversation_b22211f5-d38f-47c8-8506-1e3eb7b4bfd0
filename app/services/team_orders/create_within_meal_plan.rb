class TeamOrders::CreateWithinMealPlan

  ORDER_FIELDS = %i[
    name
    number_of_people
  ].freeze

  DELIVERY_FIELDS = %i[
    delivery_address_level
    delivery_address
    delivery_suburb
    delivery_instruction
  ].freeze

  BILLING_FIELDS = %i[
    cpo_id
    gst_free_cpo_id
    department_identity
    credit_card_id
  ].freeze

  def initialize(meal_plan:, team_order_params:, team_admin:)
    @meal_plan = meal_plan
    @team_order_params = team_order_params
    @team_admin = team_admin
    @result = Result.new(meal_plan: meal_plan)
  end

  def call
    if can_create?
      @team_order = Order.new(sanitized_params)
      result.team_order = team_order
      if team_order.save
        create_details
        attach_supplier
        send_team_admin_new_order_email
        log_event
      else
        result.errors += team_order.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :meal_plan, :team_order_params, :team_admin, :team_order, :result

  def can_create?
    case
    when meal_plan.blank? || meal_plan.kind != 'individual'
      result.errors << 'Cannot create a team order without a valid meal plan'
    when team_admin.blank? || meal_plan.customer_profile != team_admin
      result.errors << 'You do not have access to this meal plan'
    end
    result.errors.blank?
  end

  def sanitized_params
    [
      default_submission_params,
      meal_plan_order_details,
      meal_plan_delivery_details,
      meal_plan_billing_details,
      purchase_order_params,
      order_delivery_details
    ].inject(&:merge)
  end

  def default_submission_params
    {
      order_type: 'one-off',
      order_variant: 'team_order',
      status: 'pending',
      customer_profile: team_admin,
      meal_plan: meal_plan,
      unique_event_id: SecureRandom.hex(8),
      uuid: SecureRandom.uuid
    }
  end

  def meal_plan_order_details
    ORDER_FIELDS.map do |field|
      [field, meal_plan.send(field)]
    end.to_h
  end

  def meal_plan_delivery_details
    DELIVERY_FIELDS.map do |field|
      [field, meal_plan.send(field)]
    end.to_h
  end

  def meal_plan_billing_details
    BILLING_FIELDS.map do |field|
      [field, meal_plan.send(field)]
    end.to_h
  end

  def purchase_order_params
    params = {}
    params[:customer_purchase_order] = customer_purchase_order if customer_purchase_order.present?
    params[:gst_free_customer_purchase_order] = gst_free_customer_purchase_order if gst_free_customer_purchase_order.present?
    params
  end

  def customer_purchase_order
    return nil if meal_plan.cpo_id.blank?

    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: team_admin, cpo_id: meal_plan.cpo_id).call
  end

  def gst_free_customer_purchase_order
    return nil if meal_plan.gst_free_cpo_id.blank?

    @_gst_free_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: team_admin, cpo_id: meal_plan.gst_free_cpo_id).call
  end

  def order_delivery_details
    return {} if team_order_params[:delivery_date].blank?

    delivery_at = 
    {
      delivery_at: Time.zone.parse(team_order_params[:delivery_date]).change(
        hour: meal_plan.delivery_time.hour,
        min: meal_plan.delivery_time.min
      )
    }
  end

  def team_order_detail_params
    {
      cutoff_option: meal_plan.cutoff_option,
      budget: meal_plan.budget,
    }
  end

  def create_details
    TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: team_order_detail_params).call
  end

  def supplier
    @_supplier = SupplierProfile.where(id: team_order_params[:supplier_id]).first
  end

  def selected_menu_sections
    @_selected_menu_sections ||= team_order_params[:selected_menu_sections]
  end

  def attach_supplier
    order_supplier = team_order.order_suppliers.where(supplier_profile: supplier).first_or_create

    if !selected_menu_sections.nil? && selected_menu_sections.is_a?(Array) # update menu section config if passed
      menu_sections = supplier.menu_sections.where(id: selected_menu_sections)
      order_supplier.update(selected_menu_sections: menu_sections.map(&:id))
    end
  end

  def send_team_admin_new_order_email
    TeamOrders::Emails::SendAdminNewOrderEmail.new(team_order: team_order).delay(queue: :notifications).call
    team_order.team_supplier_profiles.each do |supplier|
      Suppliers::Emails::SendTeamOrderHeadsUpEmail.new(team_order: team_order, supplier: supplier).delay(queue: :notifications).call
    end
  end

  def log_event
    EventLogs::Create.new(event_object: team_order, event: 'new-team-order-created').delay(queue: :notifications).call
  end

  class Result
    attr_accessor :meal_plan, :team_order, :errors

    def initialize(meal_plan)
      @meal_plan = meal_plan
      @team_order = nil
      @errors = []
    end

    def success?
      errors.blank? && team_order.present? && team_order.persisted?
    end
  end

end
