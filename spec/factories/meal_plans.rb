FactoryBot.define do

  factory :meal_plan do

    trait :random do
      customer_profile { create(:customer_profile, :random) }
      kind { MealPlan::VALID_KINDS.sample }
      name { Faker::Name.name }
      delivery_time { Time.zone.now + rand(10..20).days }
      delivery_address { Faker::Address.street_address }
      delivery_suburb { create(:suburb, :random) }
      credit_card { create(:credit_card, :on_account_card) }
      uuid { SecureRandom.uuid }
    end

    trait :team_meal_plan do
      number_of_people { rand(10..20) }
      kind { 'individual' }
      cutoff_option { TeamOrder::Detail::VALID_CUTOFF_OPTIONS.sample }
    end

  end

end
