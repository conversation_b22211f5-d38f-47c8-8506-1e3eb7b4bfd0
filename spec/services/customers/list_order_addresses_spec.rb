require 'rails_helper'

RSpec.describe Customers::ListOrderAddresses, type: :service, orders: true do

  let!(:delivery_at) { Time.zone.now.beginning_of_year + 1.month }
  let!(:customer) { create(:customer_profile, :random) }

  let!(:suburb1) { create(:suburb, :random) }
  let!(:suburb2) { create(:suburb, :random) }

  let!(:order1) { create(:order, :delivered, customer_profile: customer, delivery_suburb: suburb1, delivery_at: delivery_at + 3.days) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, delivery_suburb: suburb1, delivery_at: delivery_at + 2.days) }
  let!(:order3) { create(:order, :delivered, customer_profile: customer, delivery_suburb: suburb2, delivery_at: delivery_at + 3.days) }
  let!(:order4) { create(:order, :delivered, customer_profile: customer, delivery_suburb: suburb2, delivery_at: delivery_at + 4.days) }

  it 'lists all the order delivery addresses belonging to the customer' do
    customer_addresses = Customers::ListOrderAddresses.new(customer: customer).call

    expect(customer_addresses.size).to eq(4)
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order2, order3, order4].map(&:delivery_address))
  end

  it 'only lists unique addressess' do
    order3.update_column(:delivery_address, order1.delivery_address)
    customer_addresses = Customers::ListOrderAddresses.new(customer: customer).call

    expect(customer_addresses.size).to eq(3)
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order2, order3, order4].map(&:delivery_address))
  end

  it 'lists all the order delivery addresses labels belonging to the customer' do
    customer_addresses = Customers::ListOrderAddresses.new(customer: customer).call
    expect(customer_addresses.map(&:label)).to include(*[order1, order2, order3, order4].map(&:delivery_address))
  end

  it 'only lists saved addresses belonging to customer\'s orders' do
    customer2 = create(:customer_profile, :random)
    [order1, order4].each do |order|
      order.update_column(:customer_profile_id, customer2.id)
    end

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order2, order3].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(*[order1, order4].map(&:delivery_address))
  end

  it 'only lists existing (non-blank) addresses' do
    [order2, order3].each do |order|
      order.update_column(:delivery_address, [nil, ''].sample)
    end

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order4].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(*[order2, order3].map(&:delivery_address))
  end

  it 'only lists addresses from active/processed orders' do
    order2.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[pending new amended confirmed delivered]).sample)

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order3, order4].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(order2.delivery_address)
  end

  it 'only lists the addresses within the passed in suburb' do
    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, suburb: suburb1, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order2].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(*[order3, order4].map(&:delivery_address))
  end

  it 'only lists addresses with a valid suburb if suburb isn\'t passed' do
    order3.update_column(:delivery_suburb_id, nil)

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, suburb: nil, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order2, order4].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(order3.delivery_address)
  end

  it 'list delivery addresses from orders delivered in the last 1 year' do
    [order1, order3].each do |order|
      order.update_column(:delivery_at, delivery_at - 2.years)
    end

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order2, order4].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(*[order1, order3].map(&:delivery_address))
  end

  it 'list delivery addresses from orders delivered in the last 2 years if it cannot find any orders in the last 1 year' do
    [order1, order3].each do |order|
      order.update_column(:delivery_at, delivery_at - 2.years) # delivered in the last 2 years
    end
    [order2, order4].each do |order|
      order.update_column(:delivery_at, delivery_at - 3.years) # delivered in the last 3 years
    end

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call
    expect(customer_addresses.map(&:street_address)).to include(*[order1, order3].map(&:delivery_address))
    expect(customer_addresses.map(&:street_address)).to_not include(*[order2, order4].map(&:delivery_address))
  end

  it 'does not list delivery addresses from orders delivered before 3 years' do
    [order1, order2, order3, order4].each do |order|
      order.update_column(:delivery_at, delivery_at - 3.years)
    end

    customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call
    expect(customer_addresses).to be_blank
  end

  context 'with delivery_address levels' do
    before do
      [order1, order4].each do |order|
        order.update_column(:delivery_address_level, "Level #{rand(2..100)}")
      end
    end

    it 'returns the delivery address level for customer orders with delivery level' do
      customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call

      expect(customer_addresses.size).to eq(4)
      expect(customer_addresses.map(&:level).compact.size).to eq(2)
      expect(customer_addresses.map(&:level)).to include(*[order1, order2, order3, order4].map(&:formatted_delivery_address_level).reject(&:blank?))
    end

    it 'returns the delivery address label for customer orders with delivery level' do
      customer_addresses = Customers::ListOrderAddresses.new(customer: customer, time: delivery_at).call

      expected_delivery_labels = [order1, order4].map do |order|
        [order.formatted_delivery_address_level, order.delivery_address].join(', ')
      end
      expect(customer_addresses.map(&:label)).to include(*expected_delivery_labels)
    end

    it 'only lists unique addressess' do
      order4.update_columns(delivery_address: order1.delivery_address, delivery_address_level: order1.delivery_address_level)
      customer_addresses = Customers::ListOrderAddresses.new(customer: customer).call

      expect(customer_addresses.size).to eq(3)
      expect(customer_addresses.map(&:level).compact.size).to eq(1)
      expect(customer_addresses.map(&:level)).to include(*[order1, order2, order3, order4].map(&:formatted_delivery_address_level).reject(&:blank?))
    end

    it 'list as unique address if delivery address (street address) is same but levels are different' do
      order4.update_columns(delivery_address: order1.delivery_address)
      customer_addresses = Customers::ListOrderAddresses.new(customer: customer).call

      expect(customer_addresses.size).to eq(4)
      expect(customer_addresses.map(&:level).compact.size).to eq(2)
      expect(customer_addresses.map(&:level)).to include(*[order1, order2, order3, order4].map(&:formatted_delivery_address_level).reject(&:blank?))
    end
  end

end
