require 'rails_helper'

RSpec.describe Suppliers::FetchMenu, type: :service, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_flags) }
  let!(:markup_override_fetcher) { double(Suppliers::FetchMarkupOverride) }
  let!(:recent_orders_fetcher) { double(Suppliers::FetchRecentOrders) }

  let(:company) { create(:company, :random) }
  let(:customer) { create(:customer_profile, :random, company: company) }

  before do
    # mock fetching markup override
    allow(Suppliers::FetchMarkupOverride).to receive(:new).and_return(markup_override_fetcher)
    allow(markup_override_fetcher).to receive(:call).and_return(nil)

    # mock fetching recent orders
    allow(Suppliers::FetchRecentOrders).to receive(:new).and_return(recent_orders_fetcher)
    allow(recent_orders_fetcher).to receive(:call).and_return([])
  end

  it 'returns successfully for a valid supplier' do
    supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

    expect(supplier_menu).to be_success
  end

  it 'return with errors if supplier is not valid' do
    supplier_menu = Suppliers::FetchMenu.new(supplier: nil).call

    expect(supplier_menu).to_not be_success
    expect(supplier_menu.errors).to include('Please select a valid Supplier')
  end

  context 'as a non-searchable supplier' do
    before do
      supplier.update_column(:is_searchable, false)
    end

    it 'returns with an error if the passed in profile is not the actual supplier' do
      profile = [
        nil,
        customer,
        create(:supplier_profile, :random, :with_flags)
      ].sample
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: profile).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('Please select an active Supplier')
    end

    it 'returns successfully when fetching menu for a non-searchable supplier as the supplier' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: supplier).call

      expect(supplier_menu).to be_success
    end
  end # non-searchable supplier

  context 'as a major supplier', woolworths: true do
    before do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
      expect(supplier).to be_is_major_supplier
    end

    it 'return with errors if supplier is a major supplier' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('Please select a non-major supplier')
    end
  end

  context 'with customer restrictions' do
    let!(:customer_profile1) { create(:customer_profile, :random) }
    let!(:customer_profile2) { create(:customer_profile, :random) }

    # restrict supplier3 to customer1
    let!(:customer_supplier_restriction) { create(:customer_profiles_supplier_profile, customer_profile: customer_profile1, supplier_profile: supplier) }

    it 'returns successfully if supplier is visible to passed in profile (customer)' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer_profile1).call

      expect(supplier_menu).to be_success
    end

    it 'returns with errors if passed in profile (customer) if supplier has customer restrictions which does not include customer' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer_profile2).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('You do not have access to this Supplier')
    end

    it 'returns with errors if profile isn\'t passed and the supplier has customer restrictions' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: nil).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('You do not have access to this Supplier')
    end

    it 'returns successfully for passed in profile if supplier does not have any restrictions' do
      supplier2 = create(:supplier_profile, :random, :with_flags)
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier2, profile: customer_profile2).call

      expect(supplier_menu).to be_success
    end

    it 'returns successfully if for_cache menu_option is passed and supplier has restrictions' do
      menu_options = { for_cache: true }
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options, profile: [nil, customer_profile2].sample).call

      expect(supplier_menu).to be_success
    end
  end

  context 'admin only supplier' do
    before do
      supplier.supplier_flags.update_column(:admin_only, true)
    end

    it 'returns with errors if supplier is admin only by default (or if is_admin is passed as false)' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: [{}, { is_admin: false }, { is_admin: nil }].sample).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('You do not have access to this Supplier')
    end

    it 'returns successfully for an admin only supplier if is_admin is passed as true' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: { is_admin: true }).call

      expect(supplier_menu).to be_success
    end
  end

  context 'with menu items' do
    let!(:menu_section1) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_item11) { create(:menu_item, :random, menu_section: menu_section1, supplier_profile: supplier) }
    let!(:menu_item12) { create(:menu_item, :random, menu_section: menu_section1, supplier_profile: supplier) }

    let!(:menu_section2) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_item21) { create(:menu_item, :random, menu_section: menu_section2, supplier_profile: supplier) }
    let!(:menu_item22) { create(:menu_item, :random, menu_section: menu_section2, supplier_profile: supplier) }

    let!(:menu_section3) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_item31) { create(:menu_item, :random, menu_section: menu_section3, supplier_profile: supplier) }
    let!(:menu_item32) { create(:menu_item, :random, menu_section: menu_section3, supplier_profile: supplier) }

    it 'returns back grouped menu items' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

      expect(supplier_menu).to be_success
      section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

      expect(section_grouped_menu_items.keys).to include(menu_section1, menu_section2, menu_section3)
      expect(section_grouped_menu_items.values.flatten).to include(menu_item11, menu_item12, menu_item21, menu_item22, menu_item31, menu_item32)
    end

    it 'does not return archived menu sections / menu items' do
      menu_section2.update_column(:archived_at, Time.now)
      menu_item32.update_column(:archived_at, Time.now)
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

      expect(supplier_menu).to be_success
      section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

      expect(section_grouped_menu_items.keys).to_not include(menu_section2)
      expect(section_grouped_menu_items.keys).to include(menu_section3)

      expect(section_grouped_menu_items.values.flatten).to include(menu_item11, menu_item12, menu_item31)
      expect(section_grouped_menu_items.values.flatten).to_not include(menu_item21, menu_item22, menu_item32)
    end

    it 'does not return hidden menu sections / menu items' do
      menu_item21.update_column(:is_hidden, true)
      menu_item31.update_column(:is_hidden, true)
      menu_item32.update_column(:is_hidden, true)
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

      expect(supplier_menu).to be_success
      section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

      expect(section_grouped_menu_items.keys).to_not include(menu_section3)
      expect(section_grouped_menu_items.keys).to include(menu_section1, menu_section2)

      expect(section_grouped_menu_items.values.flatten).to include(menu_item11, menu_item12, menu_item22)
      expect(section_grouped_menu_items.values.flatten).to_not include(menu_item21, menu_item31, menu_item32)
    end

    it 'does not return menu_section(s) and its items which are marked as custom' do
      menu_section2.update_column(:name, 'custom')
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

      expect(supplier_menu).to be_success
      section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

      expect(section_grouped_menu_items.keys).to_not include(menu_section2)
      expect(section_grouped_menu_items.values.flatten).to_not include(menu_item21, menu_item22)
    end

    context 'within a team order' do
      before do
        menu_item21.update_column(:team_order_only, true)
        menu_item31.update_column(:team_order, true)
        menu_item32.update_column(:team_order, true)
      end

      it 'does not return non-team order items if team_order_menu is passed as true' do
        menu_options = { team_order_menu: true }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections).to include(menu_section2, menu_section3)
        expect(menu_sections).to_not include(menu_section1)

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item21, menu_item31, menu_item32)
        expect(menu_items).to_not include(menu_item11, menu_item12, menu_item22)
      end

      context 'for an Individual Meal Plan (team order)', meal_plans: true, team_orders: true do
        let!(:meal_plan) { create(:meal_plan, :random, kind: 'individual', budget: rand(20.2..30.9)) }
        let!(:menu_options) { { teamMealUUID: meal_plan.uuid } }

        it 'does not return non-team order items if a valid teamMealUUID is passed' do
          supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

          expect(supplier_menu).to be_success
          section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

          menu_sections = section_grouped_menu_items.keys
          expect(menu_sections).to include(menu_section2, menu_section3)
          expect(menu_sections).to_not include(menu_section1)

          menu_items = section_grouped_menu_items.values.flatten
          expect(menu_items).to include(menu_item21, menu_item31, menu_item32)
          expect(menu_items).to_not include(menu_item11, menu_item12, menu_item22)
        end

        it 'returns the meal plan' do
          supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

          expect(supplier_menu).to be_success
          expect(supplier_menu.team_meal_plan).to eq(meal_plan)
        end
      end
    end

    it 'returns the items only from the passed in selected_menu_sections' do
      menu_options = { selected_menu_sections: [menu_section1, menu_section3].map(&:id) }
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

      expect(supplier_menu).to be_success
      section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

      menu_sections = section_grouped_menu_items.keys
      expect(menu_sections.size).to eq(2)
      expect(menu_sections).to include(menu_section1, menu_section3)
      expect(menu_sections).to_not include(menu_section2)

      menu_items = section_grouped_menu_items.values.flatten
      expect(menu_items).to include(menu_item11, menu_item12, menu_item31, menu_item32)
      expect(menu_items).to_not include(menu_item21, menu_item22)
    end

    context 'for items within budget' do
      let!(:budget) { 13.31 }
      let!(:within_range_price) { (8..11).to_a.sample } # range of 9.68 to 13.31 with markup and gst

      before do
        supplier.update_column(:markup, 10) # 10% markup
        [menu_item11, menu_item21, menu_item31].each do |item|
          item.update_column(:price, 15)
        end
        [menu_item12, menu_item22, menu_item32].each do |item|
          item.update_column(:price, within_range_price)
        end
      end

      it 'returns the items whose markup price (inc gst) is within the budget' do
        menu_options = { within_budget: budget }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item12, menu_item22, menu_item32)
        expect(menu_items).to_not include(menu_item11, menu_item21, menu_item31)
      end

      context 'with markup override', markup_overrides: true do
        let!(:markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company, markup: 55) }

        before do
           allow(markup_override_fetcher).to receive(:call).and_return(markup_override)
        end

        it 'returns the items whose overriden markup price (inc gst) is within the budget' do
          # just to make it slightly lower than overriden markup price but not supplier markup
          menu_item22.update_column(:price, 7)

          menu_options = { within_budget: budget }
          supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options, profile: customer).call

          expect(supplier_menu).to be_success
          section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
          menu_items = section_grouped_menu_items.values.flatten
          expect(menu_items).to include(menu_item22)
          expect(menu_items).to_not include(menu_item12, menu_item32) # with price from 8-11 (within_range_price) goes over budget for 55% markup
          expect(menu_items).to_not include(menu_item11, menu_item21, menu_item31)
        end
      end

      it 'returns the items whose rate card price (inc gst) is within budget' do
        menu_item21.update_column(:name, 'asdasd')
        _item_rate_card = create(:rate_card, :random, menu_item: menu_item21, company: company, price: 10) # gst price of 13.20
        menu_options = { within_budget: budget }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options, profile: customer).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item21)
      end

      context 'with serving size' do
        let!(:serving_size121) { create(:serving_size, :random, menu_item: menu_item11, price: within_range_price) }
        let!(:serving_size122) { create(:serving_size, :random, menu_item: menu_item11, price: within_range_price) }

        let!(:serving_size221) { create(:serving_size, :random, menu_item: menu_item21, price: 20) }
        let!(:serving_size222) { create(:serving_size, :random, menu_item: menu_item21, price: within_range_price) }

        let!(:serving_size321) { create(:serving_size, :random, menu_item: menu_item31, price: 30) }
        let!(:serving_size322) { create(:serving_size, :random, menu_item: menu_item31, price: 25) }

        it 'returns the items with serving sizes wherein atleast 1 serving size is within budget' do
          menu_options = { within_budget: budget }
          supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

          expect(supplier_menu).to be_success
          section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
          menu_items = section_grouped_menu_items.values.flatten
          expect(menu_items).to include(menu_item11)
          expect(menu_items).to include(menu_item21) # partially above budget
          expect(menu_items).to_not include(menu_item31)
        end

        it 'returns the items with serving sizes wherein at-least 1 unarchived serving size is within budget' do
          serving_size222.update_column(:archived_at, Time.zone.now)
          menu_options = { within_budget: budget }
          supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

          expect(supplier_menu).to be_success
          section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
          menu_items = section_grouped_menu_items.values.flatten
          expect(menu_items).to include(menu_item11)
          expect(menu_items).to_not include(menu_item21)
          expect(menu_items).to_not include(menu_item31)
        end

        context 'with markup override', markup_overrides: true do
          let!(:markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company, markup: 55) }

          before do
             allow(markup_override_fetcher).to receive(:call).and_return(markup_override)
          end

          it 'returns the items with serving sizes wherein atleast 1 serving size (with overriden markup) is within budget' do
            # just to make it slightly lower than overriden markup price but not supplier markup
            serving_size322.update_column(:price, 7)

            menu_options = { within_budget: budget }
            supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options, profile: customer).call

            expect(supplier_menu).to be_success
            section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
            menu_items = section_grouped_menu_items.values.flatten

            expect(menu_items).to include(menu_item31)
            expect(menu_items).to_not include(menu_item11, menu_item21) # all serving sizes are above budget
            expect(menu_items).to_not include(menu_item12, menu_item22, menu_item32) # with price from 8-11 (within_range_price) goes over budget for 55% markup
          end
        end

        it 'returns the items whose at-least 1 serving size\'s rate card price (inc gst) is within budget' do
          menu_item21.update_column(:name, 'asdasd')
          company = create(:company, :random)
          customer = create(:customer_profile, :random, company: company)
          _item_rate_card = create(:rate_card, :random, menu_item: menu_item31, serving_size: serving_size322, company: company, price: 10) # gst price of 13.20
          menu_options = { within_budget: budget }
          supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options, profile: customer).call

          expect(supplier_menu).to be_success
          section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
          menu_items = section_grouped_menu_items.values.flatten
          expect(menu_items).to include(menu_item31)
        end
      end # serving size
    end # within budget menu

    context 'menu section filtering' do
      it 'gets the items for the passed in menu_section' do
        menu_options = { menu_section: menu_section2 }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections).to include(menu_section2)
        expect(menu_sections).to_not include(menu_section1, menu_section3)

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item21, menu_item22)
        expect(menu_items).to_not include(menu_item11, menu_item12, menu_item31, menu_item32)
      end

      it 'gets the items for the passed in search query' do
        [menu_item11, menu_item21, menu_item31].each{|item| item.update_column(:name, 'Same Name') }

        menu_options = { query: menu_item21.name }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections.size).to eq(1)
        expect(menu_sections).to_not include(menu_section1, menu_section2, menu_section3)

        search_menu_section = menu_sections.first
        expect(search_menu_section.name).to eq("Searched for: #{menu_options[:query]}")

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item11, menu_item21, menu_item31)
        expect(menu_items).to_not include(menu_item12, menu_item22, menu_item32)
      end
    end # menu section filtering

    context 'with passed in mealUUID', meal_plans: true do
      let!(:meal_plan_category) { create(:category, :random) }

      before do
        meal_plan_category.update_column(:slug, Category::MEAL_PLAN_CATEGORY_SLUGS.sample)

        [menu_section2, menu_section3].each do |menu_section|
          menu_section.update(categories: [meal_plan_category])
        end
      end

      it 'only list the items whose menu section has a meal plan category' do
        menu_options = { mealUUID: SecureRandom.uuid }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer, menu_options: menu_options).call

        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections).to include(menu_section2, menu_section3)
        expect(menu_sections).to_not include(menu_section1)

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item21, menu_item22, menu_item31, menu_item32)
        expect(menu_items).to_not include(menu_item11, menu_item12)
      end
    end

    context 'with customer favourites' do
      let(:customer) { create(:customer_profile, :random, :with_user) }
      let!(:favourite1) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item11, created_at: Time.zone.now - 1.day) }
      let!(:favourite2) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item22, created_at: Time.zone.now) }

      it 'returns the favourites for the passed in customer (sorted by LIFO)' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.favourite_menu_items).to include(menu_item11, menu_item22)
        expect(supplier_menu.favourite_menu_items).to_not include(menu_item12, menu_item21, menu_item31, menu_item32)

        # sorted
        expect(supplier_menu.favourite_menu_items[0]).to eq(menu_item22)
        expect(supplier_menu.favourite_menu_items[1]).to eq(menu_item11)
      end

      it 'does not return any favourites if profile is not passed' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: nil).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.favourite_menu_items).to be_empty
      end

      it 'does not return any favourites if profile is not a customer' do
        supplier2 = create(:supplier_profile, :random, :with_flags)
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: supplier2).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.favourite_menu_items).to be_empty
      end

      it 'returns with a (custom) favourite menu section' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        grouped_menu_section = supplier_menu.section_grouped_menu_items.to_h
        favourite_menu_section = grouped_menu_section.keys.detect{|section| section.id == -1 }
        expect(favourite_menu_section).to be_present
        expect(favourite_menu_section.name).to eq('Favourites')
        expect(favourite_menu_section.group_name).to eq('My Favourites')
      end

      it 'returns with a (custom) favourite menu section along with the favourites' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        favourite_menu_section_hash = supplier_menu.section_grouped_menu_items.detect{|section, _| section.id == -1 }

        favourite_menu_section = favourite_menu_section_hash.first
        expect(favourite_menu_section).to be_present
        expect(favourite_menu_section.name).to eq('Favourites')
        expect(favourite_menu_section.group_name).to eq('My Favourites')

        favourite_items = supplier_menu.section_grouped_menu_items.to_h[favourite_menu_section]
        expect(favourite_items).to include(menu_item11, menu_item22)
        expect(favourite_items).to_not include(menu_item12, menu_item21, menu_item31, menu_item32)
      end

      it 'only gets the favourites items (sorted by LIFO)' do
        menu_options = { favourites_only: true }
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections.size).to eq(1)
        expect(menu_sections).to_not include(menu_section1, menu_section2, menu_section3)

        favourite_menu_section = menu_sections.first
        expect(favourite_menu_section.name).to eq('Favourites')
        expect(favourite_menu_section.group_name).to eq('My Favourites')

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item11, menu_item22)
        expect(menu_items).to_not include(menu_item12, menu_item21, menu_item31, menu_item32)

        # sorted
        expect(menu_items[0]).to eq(menu_item22)
        expect(menu_items[1]).to eq(menu_item11)
      end
    end # with customer favourites

    context 'with rate cards' do
      let(:company) { create(:company, :random) }
      let(:customer) { create(:customer_profile, :random, company: company) }
      let!(:rate_card1) { create(:rate_card, :random, menu_item: menu_item12, company: company) }
      let!(:rate_card2) { create(:rate_card, :random, menu_item: menu_item31, company: company) }

      it 'returns (menu_item) grouped rate cards for the passed in customer' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.grouped_rate_cards.keys).to include(menu_item12.id, menu_item31.id)
        expect(supplier_menu.grouped_rate_cards.values.flatten).to include(rate_card1, rate_card2)
      end

      it 'does not return any rate cards if profile is not passed' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: nil).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.grouped_rate_cards).to be_blank
      end

      it 'does not return any rate cards if passed in profile is a supplier' do
        profile = create(:supplier_profile, :random, :with_flags)
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: profile).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.grouped_rate_cards).to be_blank
      end

      it 'only lists rate cards from the returned menu items' do
        menu_item33 = create(:menu_item, :random, menu_section: menu_section3, supplier_profile: supplier, is_hidden: true) # hidden menu item
        rate_card3 = create(:rate_card, :random, menu_item: menu_item33, company: company)

        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.grouped_rate_cards.keys).to_not include(menu_item33.id)
        expect(supplier_menu.grouped_rate_cards.values.flatten).to_not include(rate_card3)
      end
    end # with rate cards

    context 'with stock quantity checks (supplier.has_skus)' do
      before do
        supplier.supplier_flags.update_column(:has_skus, true)

        menu_item11.update_column(:stock_quantity, nil)
        menu_item12.update_column(:stock_quantity, rand(1..10))

        menu_item21.update_column(:stock_quantity, 0)
        menu_item22.update_column(:stock_quantity, rand(1..10))

        menu_item31.update_column(:stock_quantity, 0)
        menu_item32.update_column(:stock_quantity, 0)
      end

      it 'only returns items with non-zero stock quantity for a supplier who has SKUs' do
        supplier_menu = Suppliers::FetchMenu.new(supplier: supplier).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h
        fetched_menu_sections = section_grouped_menu_items.keys

        expect(fetched_menu_sections).to include(menu_section1, menu_section2)
        expect(fetched_menu_sections).to_not include(menu_section3)

        fetched_menu_items = section_grouped_menu_items.values.flatten
        expect(fetched_menu_items).to include(menu_item11, menu_item12, menu_item22)
        expect(fetched_menu_items).to_not include(menu_item21, menu_item31, menu_item32)
      end
    end # stock quantity check
  end # with menu items

  context 'with supplier markup overrides', markup_overrides: true do
    let!(:markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company, markup: 55) }

    before do
      allow(markup_override_fetcher).to receive(:call).and_return(markup_override)
    end

    it 'requests to fetch the markup override for the suppliers and customer' do
      expect(Suppliers::FetchMarkupOverride).to receive(:new).with(supplier: supplier, customer: customer, company: company, required_override: :markup)

      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call
      expect(supplier_menu).to be_success
    end

    it 'returns the supplier markup override for the passed in customer' do
      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.markup_override).to eq(markup_override)
    end

    it 'doesn\'t fetch markup override if passed in profile is not a customer' do
      expect(Suppliers::FetchMarkupOverride).to_not receive(:new)

      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: [nil, supplier].sample).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.markup_override).to be_blank
    end
  end # markup overrides

  context 'with recent orders' do
    let!(:customer) { create(:customer_profile, :random) }
    let!(:order1) { create(:order, :confirmed) }
    let!(:order2) { create(:order, :confirmed) }

    before do
      allow(recent_orders_fetcher).to receive(:call).and_return([order1, order2])
    end

    it 'requests to fetch recent orders for the passed in customer profile and returns them' do
      expect(Suppliers::FetchRecentOrders).to receive(:new).with(supplier: supplier, customer: customer)

      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_present
      expect(supplier_menu.recent_orders.size).to eq(2)

      expect(supplier_menu.recent_orders).to include(order1, order2)
    end

    it 'does not request to fetch recent orders if passed in menu option is for team order menu' do
      expect(Suppliers::FetchRecentOrders).to_not receive(:new)

      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: { team_order_menu: true }, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_blank
    end

    it 'does not request to fetch recent orders if passed in order is a non-draft order' do
      non_draft_order = create(:order, status: (Order::VALID_ORDER_STATUSES - ['draft']).sample)

      expect(Suppliers::FetchRecentOrders).to_not receive(:new)

      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, profile: customer, order: non_draft_order).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_blank
    end

    it 'does not request to fetch recent orders if not fetching the full menu' do
      option = %i[category_group query menu_section favourites_only].sample
      menu_options = {}
      menu_options[option] = case option
      when :menu_section
        create(:menu_section, :random, supplier_profile: supplier)
      when :favourites_only
        true
      else # :category_group / :query
        'random-string'
      end
      expect(Suppliers::FetchRecentOrders).to_not receive(:new)

      supplier_menu = Suppliers::FetchMenu.new(supplier: supplier, menu_options: menu_options, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_blank
    end
  end # recent orders

end
