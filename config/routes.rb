Rails.application.routes.draw do
  use_doorkeeper
  get 'errors/not_found'

  get 'errors/internal_server_error'

  # Defining home page of the application
  # NOTE :: nothing goes above it! Nothing!!
  root 'pages#home'
  defaults subdomain: (yordar_credentials(:prismic_subdomain) || '') do
    get '/' => 'pages#home', as: :prismic_root
  end

  get 'sitemap.xml', to: 'sitemap#index', defaults: { format: 'xml' }

  # YOR-466 this is the landing page for migrating users from https://www.caitre-d.com to Yordar
  get 'caitred' => 'campaigns#caitred'

  resources :order_reviews, only: %i[new create]

  namespace :docusign do
    resources :webhooks, only: :create
  end

  resources :locations, only: :show, param: :location
  get 'locations/:location/:suburb' => 'locations#show', as: :location_suburb
  defaults subdomain: (yordar_credentials(:prismic_subdomain) || '') do
    get 'office-catering/:location/(:suburb)' => 'locations#show', as: :prismic_location
  end
  get 'category/:category/:location' => 'category_locations#show', as: :category_location

  # Ordering process routes
  # ==============================
  # Order process, 7 steps in total, suppliers -> menu -> summary -> checkout
  # -> confirmation -> submit -> success
  resources :suppliers, only: %i[index show]

  get 'search' => 'suppliers#index', as: :order_step1
  get 'show/(:slug)' => 'suppliers#show', as: :order_step2
  get 'order/summary' => 'orders#summary', as: :order_step3
  get 'order/checkout' => 'orders#checkout', as: :order_step4

  # NEXT App routes
  defaults subdomain: (yordar_credentials(:next_app_subdomain) || ''), port: (yordar_credentials(:next_app_port) || '') do
    get 'search/:category_group/:state/:suburb' => 'suppliers#index', as: :next_app_supplier_search, constraints: { category_group: /[a-zA-Z\-_\/]+/, state: /[a-zA-Z\-_\/]+/, suburb: /[a-zA-Z\-_\/]+/ }
    get 'show/:slug' => 'suppliers#show', as: :next_app_supplier_show, constraints: { slug: /[a-zA-Z0-9\-_\/]+/ }
    get '/quotes' => 'pages#show', as: :next_app_customer_quotes
    get '/quotes/:uuid' => 'pages#show', as: :next_app_customer_specific_quotes
    get 'employee-survey/:uuid' => 'pages#show', as: :employee_survey
    get 'checkout' => 'pages#show', as: :order_checkout
    get 'checkout/custom' => 'pages#show', as: :new_custom_order
    get 'team-order/attendee-package/:code' => 'pages#show', as: :team_order_attendee_package
    get 'team-order/attendee-order/:code' => 'pages#show', as: :next_app_team_order_attendee_order
    get 'checkout/edit/:id' => 'pages#show', as: :next_app_order_edit
    get 'loading-dock/:uuid/:request_uuid' => 'pages#show', as: :loading_dock_request
    get 'loading-dock/:uuid' => 'pages#show', as: :loading_dock
  end

  # ============= Old routes (only import as needed) ================

  get 'invoice/pay/:id' => 'invoices#pay', as: :pay_invoice
  post 'invoice/payment' => 'invoices#process_payment', as: :invoice_payment

  # for links within emails # can be removed after a few months from may 2022
  get 'orders/edit/:id' => redirect {|params, _| "/c_profile/orders/edit/#{params[:id]}" }

  get 'orders/switch/:id' => 'orders#switch', as: :switch_recurrent
  get 'cart/clear' => 'orders#clear_cart', as: :clear_cart

  # Protected area routes
  # ==============================
  # customer dashboard
  scope '/c_profile' do
    get 'orders/:id' => 'orders#show', as: :order_show
    get 'orders/edit/:id' => 'orders#edit', as: :order_edit
    get 'orders/add-more/:id' => 'orders#add_more_products', as: :add_more_order_products
    put 'update/:id' => 'customer_profiles#update', as: :customer_profile_update

    get 'meal-plans' => 'customer_profiles#meal_plans', as: :customer_meal_plans
    get 'team-orders' => 'team_orders#index', as: :customer_team_orders, order_type: 'team-order'
    get 'new-team-order' => 'team_orders#new', as: :customer_new_team_orders
    get 'new-recurring-team-order' => 'team_orders#new', as: :customer_new_recurring_team_orders, recurring_team_order: true
    get 'contacts' => 'event_attendees#index', as: :customer_contacts
    get 'quotes' => 'customer_profiles#quotes', as: :customer_quotes
    get 'my_suppliers' => 'customer_profiles#my_suppliers', as: :customer_my_suppliers
    get 'invoices' => 'invoices#index', as: :customer_invoices
    get 'purchase-orders' => 'purchase_orders#index', as: :customer_purchase_orders
    get 'payment-options' => 'customer_profiles#payment_options', as: :customer_payment_options
    get 'account-and-billing' => 'customer_profiles#account_and_billing', as: :customer_account_and_billing
    get 'customer-settings' => 'customer_profiles#customer_settings', as: :customer_settings
    get 'saved-addresses' => 'saved_addresses#index', as: :customer_saved_addresses
    get 'notification-preferences' => 'customer_profiles#notification_preferences', as: :customer_notification_preferences
    get 'request-admin-access' => 'customer_profiles#request_admin_access', as: :request_admin_access
    get 'reports' => 'reports#index', as: :customer_reports
    get 'employee-surveys' => 'customer_profiles#employee_surveys', as: :customer_employee_surveys

    get 'resend_confirmation' => 'customer_profiles#resend_confirmation', as: :resend_confirmation

    root 'customer_profiles#show', as: :customer_profile
  end

  # supplier dashboard
  scope '/s_profile' do
    get '/orders/:id' => 'orders#show_for_supplier', as: :supplier_order_show
    get 'menu' => 'supplier_profiles#menu', as: :supplier_menu
    get 'category-settings' => 'minimums#index', as: :supplier_minimums
    get 'delivery_zones' => 'delivery_zones#index', as: :supplier_delivery_zones
    get 'closure_dates' => 'supplier_closures#index', as: :supplier_closure_dates
    get 'rating' => 'supplier_profiles#ratings', as: :supplier_ratings
    get 'invoices' => 'supplier_invoices#index', as: :supplier_invoices
    get 'reports' => 'reports#index', as: :supplier_reports
    get 'account' => 'supplier_profiles#account', as: :supplier_account
    get 'settings' => 'supplier_profiles#settings', as: :supplier_settings
    get 'notification-preferences' => 'supplier_profiles#notification_preferences', as: :supplier_notification_preferences

    put 'update/:id' => 'supplier_profiles#update', as: :supplier_profile_update
    get 'send-agreement' => 'supplier_profiles#send_agreement', as: :send_supplier_agreement
    get 'fetch-agreement' => 'supplier_profiles#fetch_agreement', as: :fetch_supplier_agreement, latest: true
    get 'search' => 'supplier_profiles#search', as: :supplier_search, defaults: { format: :json }
    get 'settings/:id/:field/(:value)' => 'supplier_profiles#anonymous_settings', as: :supplier_anonymous_settings

    root 'supplier_profiles#show', as: :supplier_profile
  end

  # admin dashboard
  scope '/admin' do
    get 'staff-on-boarding' => 'admin#staff_on_boarding', as: :staff_on_boarding
    get 'customers' => 'admin#customers', as: :customers_admin
    get 'admins' => 'admin#admins', as: :admins_admin
    get 'companies' => 'admin#companies', as: :companies_admin
    get 'suppliers' => 'admin#suppliers', as: :suppliers_admin
    get 'invoices' => 'admin#invoices', as: :invoices_admin
    get 'invoice-summary' => 'admin#invoice_summary', as: :invoice_summary_admin
    get 'orders' => 'admin#orders', as: :orders_admin
    get 'custom-orders' => 'admin#orders', as: :custom_orders_admin, defaults: { custom_orders_only: true }
    get 'spends-reports' => 'admin#spends_reports', as: :spends_reports_admin
    get 'sales-reports' => 'admin#sales_reports', as: :sales_reports_admin
    get 'pantry-managers' => 'admin#pantry_managers', as: :pantry_managers_admin
    get 'notifications' => 'admin#notifications', as: :admin_notifications
    get 'coupons' => 'admin#coupons', as: :coupons_admin
    get 'holidays' => 'admin#holidays', as: :holidays_admin
    get 'promotions' => 'admin#promotions', as: :promotions_admin
    get 'reminders' => 'admin#reminders', as: :reminders_admin
    get 'woolworths-accounts' => 'admin#woolworths_accounts', as: :woolworths_accounts_admin

    # model (data) based admin
    get 'categories' => 'admin#data', as: :categories_admin, model_name: 'Category'
    get 'suburbs' => 'admin#data', as: :suburbs_admin, model_name: 'Suburb'
    get 'email-templates' => 'admin#data', as: :email_templates_admin, model_name: 'EmailTemplate'
    get 'dear-accounts' => 'admin#data', as: :dear_accounts_admin, model_name: 'Dear::Account'
    get 'dear-categories' => 'admin#data', as: :dear_categories_admin, model_name: 'Dear::Category'
    get 'ouath-applications' => 'admin#data', as: :ouath_applications_admin, model_name: 'OauthApplication'

    root 'admin#dashboard', as: :admin
  end

  get 'ffc_profile' => 'ffc_profiles#show', as: :ffc_profile

  # Shared
  # ----------------------
  match 'order/:order_id/:profile_type/:profile_id/:mode/:hashed_value' => 'orders#confirm_or_reject', as: :order_confirm_or_reject, via: %i[get post]
  match 'quote-order/:order_id/:profile_type/:profile_id/:mode/:hashed_value' => 'orders#approve_or_reject', as: :order_approve_or_reject, via: %i[get post]

  # Supplier section (html)
  # ----------------------
  controller :supplier_profiles do
    get 'export_menu' => :export_menu, as: :export_menu
    post 'import_menu' => :import_menu, as: :import_menu
  end

  resources :team_orders do
    member do
      get 'clone'
      get 'extend'
    end
  end

  get 'new-contact/:team_admin_id' => 'event_attendees#new', as: :new_event_attendee_invite
  resources :event_attendees, only: :create

  # team order attendee links
  get 'team-order-invite/:event_id' => 'team_order_attendees#new', as: :new_team_order_attendee_invite
  match 'team-orders/attendee-decline/:code' => 'team_order_attendees#unsubscribe', as: :team_order_attendee_unsubscribe, via: %i[get post]
  get 'team-orders/attendee-order/:code' => 'team_order_attendees#order', as: :team_order_attendee_order

  resources :team_order_attendees, only: :create

  # package links
  get 'team-orders/package/:id' => 'team_orders#show_package', as: :team_order_package
  get 'team-order-package-invite/:package_id' => 'team_order_attendees#new_for_package', as: :new_team_order_package_attendee_invite
  get 'team-orders/attendee-package-order/:code' => 'team_order_attendees#show_package', as: :team_order_attendee_package_order
  match 'team-orders/attendee-package-decline/:code' => 'team_order_attendees#unsubscribe_package', as: :team_order_attendee_package_unsubscribe, via: %i[get post]

  # registred package team order
  get 'team-orders/attendee-registered-package-order/:code/:event_id' => 'team_order_attendees#new_within_package', team_order_menu: true, as: :team_order_attendee_registered_package_order
  delete 'team-orders/attendee-detach-registered-package-order/:code/:event_id' => 'api/team_order_attendees#destroy_package_attendee', team_order_menu: true, as: :team_order_attendee_detach_registered_package_order
  get 'team-orders/attendee-decline-registered-package-order/:code/:event_id' => 'team_order_attendees#unsubscribe_open_package_order', team_order_menu: true, as: :team_order_attendee_unsubscribe_registered_package_order
  post 'team-orders/attendee-decline-registered-package-order/:code/:event_id' => 'team_order_attendees#unsubscribe_open_package_order', team_order_menu: true, as: :team_order_attendee_unsubscribe_registered_package_order_post

  # Rails Admin section (reporting)
  # ----------------------
  scope :accounting do
    post 'report' => 'accounting#report', as: :accounting_report, defaults: { format: :csv }
  end

  # Json services
  # ==============================
  namespace :api do
    resources :holidays, only: :index, defaults: { format: :json }
    resources :leads, only: :create, defaults: { format: :json }
    resources :companies, only: :index, defaults: { format: :json }
    resources :custom_orders, only: %i[create destroy], defaults: { format: :json } do
      collection do
        get 'fetch_suppliers' => 'custom_orders#fetch_suppliers', as: :fetch_suppliers, defaults: { format: :json }
        get 'supplier_menu/:slug' => 'custom_orders#supplier_menu', as: :supplier_menu, defaults: { format: :json }
        get 'suppliers' => 'custom_orders#suppliers', as: :suppliers, defaults: { format: :json }
      end
    end
    post 'event_attendees/create_from_csv' => 'event_attendees#create_from_csv'
    resources :event_attendees, only: %i[create destroy] do
      post :team_update, context: 'team_contact_list'
    end
    resources :suppliers, only: %i[show update] do
      collection do
        get 'customer-suppliers' => 'suppliers#customer_suppliers', defaults: { format: :json }
        get 'cache-list' => 'suppliers#cache_list', defaults: { format: :json }
      end
      get 'delivery-details' => 'suppliers#delivery_details', defaults: { format: :json }
      get 'arvhive-menu' => 'suppliers#archive_menu', as: :archive_menu
      get 'menu' => 'suppliers#menu', as: :menu, defaults: { format: :json }
      get 'order-summary' => 'suppliers#fetch_order_summary', as: :fetch_order_summary, defaults: { format: :json }
    end
    resources :purchase_orders do
      collection do
        get 'orders' => 'purchase_orders#orders', defaults: { format: :json }
        put 'update-order/:order_id' => 'purchase_orders#update_order', as: :update_order, defaults: { format: :json }
      end
    end
    resources :budgets, defaults: { format: :json }
    resources :saved_addresses
    resources :invoices, only: %i[index create update] do
      post 'attach-orders' => 'invoices#attach_orders', as: :attach_orders, defaults: { format: :json }
      get 'orders' => 'invoices#invoice_orders', as: :orders, defaults: { format: :json }
      get 'generate-documents' => 'invoices#generate_documents', as: :generate_documents, defaults: { format: :json }
    end
    resources :employee_surveys do
      post 'submit' => 'employee_surveys#submit', as: :submit_survey
      resources :employee_survey_submissions, only: :index
    end
    resources :survey_questions
    resources :supplier_closures
    resources :event_teams, only: %i[create update destroy]
    resources :team_order_attendees, only: %i[update show destroy], defaults: { format: :json } do
      post 'checkout' => 'team_order_attendees#checkout'
      collection do
        get 'package/:code' => 'team_order_attendees#show_package', defaults: { format: :json }
      end
    end
    resources :order_lines, only: %i[create update destroy], defaults: { format: :json }
    resources :locations, only: %i[create update destroy], defaults: { format: :json }
    resources :menu_sections

    resources :menu_items do
      post 'clone'
      get 'check-usage' => 'menu_items#check_usage', as: :check_usage, defaults: { format: :json }
      collection do
        get 'search', defaults: { format: :json }
      end
    end
    resources :minimums, defauts: { format: :json }
    resources :suburbs, only: :index, defaults: { format: :json }
    resources :rate_cards, only: %i[create update destroy], defaults: { format: :json }
    resources :favourite_menu_items, only: %i[index update destroy]
    resources :favourite_suppliers, only: %i[update destroy]
    resources :serving_sizes
    resources :menu_extra_sections
    resources :menu_extras
    resources :delivery_zones
    resources :orders do
      collection do
        get 'from-session' => 'orders#show', from_session: true
        get 'checkout' => 'orders#checkout'
        post 'recurrent' => 'orders#make_recurrent', as: :make_recurrent
        get 'validate_checkout_date' => 'orders#validate_checkout_date', as: :validate_checkout_date
        get 'export-pdf' => 'orders#export_pdf', as: :export_pdf
      end
      get 'clone' => 'orders#clone', as: :clone
      get 'validate' => 'orders#validate', as: :validate
      get 'validate_woolworths_order' => 'orders#validate_woolworths_order', as: :validate_woolworths_order
      get 'check_swipe_card_access' => 'orders#check_swipe_card_access', as: :check_swipe_card_access
      get 'check_closure_dates' => 'orders#check_closure_dates', as: :check_closure_dates
      get 'check_lead_time' => 'orders#check_lead_time', as: :check_lead_time
      get 'check_minimum_spend' => 'orders#check_minimum_spend', as: :check_minimum_spend
      get 'validate_checkout_date' => 'orders#validate_checkout_date', as: :validate_order_checkout_date
      get 'check_supplier_suburb_availability' => 'orders#check_supplier_suburb_availability', as: :check_supplier_suburb_availability
      post 'submit' => 'orders#submit', as: :submit
      post 'amend' => 'orders#amend', as: :amend
      put 'reactivate' => 'orders#reactivate', as: :reactivate
      put 'confirm_order' => 'orders#confirm_order', as: :confirm_order
      put 'reject_order' => 'orders#reject_order', as: :reject_order
      post 'attach_coupon' => 'orders#attach_coupon', as: :attach_coupon
      match 'update_commission' => 'orders#update_commission', as: :update_commission, via: %i[post put]
      get 'fetch_delivery_windows' => 'orders#fetch_delivery_windows', as: :fetch_delivery_windows
      resources :loading_docks, only: %i[index new create], defaults: { format: :json }
    end
    resources :team_orders, only: %i[index create] do
      get 'generate-order-manifest' => 'team_orders#generate_order_manifest', as: :generate_order_manifest, defaults: { format: :json }
      collection do
        get 'become_team_admin' => 'team_orders#become_team_admin', as: :become_team_admin, defaults: { format: :json }
        get 'refresh_suppliers_list' => 'team_orders#refresh_suppliers_list', as: :refresh_suppliers_list
        get 'retrieve_supplier_menu' => 'team_orders#retrieve_supplier_menu', as: :retrieve_supplier_menu
        get 'fetch_supplier_cutoff_hours_remaining' => 'team_orders#fetch_supplier_cutoff_hours_remaining', as: :fetch_supplier_cutoff_hours_remaining
        get 'fetch_suppliers_availability' => 'team_orders#fetch_suppliers_availability', as: :fetch_suppliers_availability
      end
    end

    resources :customers, only: :update do
      collection do
        get 'order_suppliers' => 'customers#order_suppliers', as: :order_suppliers, defaults: { format: :json }
        get 'checkout_details' => 'customers#checkout_details', as: :customer_checkout_details
        post 'become-company-team-admin' => 'customers#become_company_team_admin', as: :become_company_team_admin, defaults: { format: :json }
      end
    end

    resources :categories, only: :index
    resources :credit_cards, only: %i[create update]
    resources :stripe_credit_cards, only: %i[create update]
    resources :reports, only: :index
    resources :hubspot, only: :index
    resources :quotes, only: %i[index show create destroy] do
      collection do
        get 'customer/:uuid' => 'quotes#customer', as: :quotes_customer, defaults: { format: :json }
      end
    end
    resources :meal_plans do
      get 'orders' => 'meal_plans#orders', as: :orders
    end

    namespace :notification do
      resources :preferences, only: %i[create update]
    end

    namespace :gatsby do
      scope format: true, constraints: { format: 'json' } do
        get '/status/:type' => 'status#fetch'
      end
    end

    namespace :external do
      scope format: true, constraints: { format: 'json' } do
        resources :orders, only: %i[index show]
        resources :menu, only: %i[index create]
      end
    end

    namespace :admin, defaults: { format: :json } do
      resources :users, only: %i[show update] do
        post 'deprecate-user' => 'users#deprecate_user', as: :deprecate_user
      end
      resources :admins, only: %i[index update] do
        collection do
          get 'pantry-manager-spends' => 'admins#pantry_manager_spends', as: :pantry_manager_spends
          post 'send-staffing-logs' => 'admins#send_staffing_logs', as: :send_staffing_logs
          post 'clear-cache' => 'admins#clear_cache', as: :clear_cache
        end
      end
      resources :customers, only: %i[index update] do
        post 'mark-as-favourite' => 'customers#mark_as_favourite', as: :mark_customer_as_favourite
        get 'send-invitation-link' => 'customers#send_invitation_link', as: :send_customer_invitation_link
        post 'invite-customer' => 'customers#invite_customer', as: :invite_customer
        post 'add-adminable-customer' => 'customers#add_adminable_customer', as: :add_adminable_customer
        resources :access_permissions, only: %i[index create]
        resources :delivery_overrides, only: %i[index create]
      end
      resources :companies, only: %i[index create update]
      resources :suppliers, only: %i[index update] do
        resources :supplier_markup_overrides, only: %i[index create]
      end
      resources :invoices, only: %i[index show update] do
        post 'notify_customer' => 'invoices#notify_customer', as: :notify_customer
      end
      resources :aged_receivables, only: :index
      resources :orders, only: %i[index show]
      resources :notifications, only: %i[index show] do
        post 'mark-as-viewed' => 'notifications#mark_as_viewed', as: :mark_notification_as_viewed
        post 'mark-as-assigned' => 'notifications#mark_as_assigned', as: :mark_notification_as_assigned
        collection do
          post 'mark-all-as-viewed' => 'notifications#mark_all_as_viewed', as: :mark_all_notifications_as_viewed
        end
      end
      resources :coupons, only: %i[index create update]
      resources :suburbs, only: %i[index create update]
      resources :holidays
      resources :promotions, only: %i[index create update] do
        resources :subscriptions
      end
      resources :reminders, only: %i[index show create update destroy]
      resources :woolworths_accounts, only: %i[index create update]
      resources :staff_details, only: %i[index create]
      resources :models, only: %i[index create update] do
        collection do
          get 'admin-config' => 'models#admin_config', as: :admin_config
        end
      end # models
    end # namespace admin

  end # namespace API

  namespace :stripe do
    resources :webhooks, only: :create
  end

  namespace :xero do
    resources :webhooks, only: :create
  end

  # ==============================
  # Route dependency in Homepage
  # ==============================

  defaults subdomain: (yordar_credentials(:prismic_subdomain) || '') do
    match 'contact-us' => 'forms#contact_us', as: :prismic_contact_us, via: %i[get post]
  end

  get 'supplier_registration' => redirect('/supplier-registration')
  get 'supplier-registration' => 'supplier_registrations#new', as: :new_supplier_registration
  resources :supplier_registrations, only: :create

  # For user login/logout etc
  devise_for :users,
    controllers: {
      registrations: 'registrations',
      sessions: 'sessions',
      passwords: 'passwords',
      confirmations: 'confirmations'
    },
    path: '',
    path_names: {
      sign_in: 'login',
      sign_up: 'register',
      sign_out: 'logout'
    }

  devise_scope :user do
    get 'staff-registration' => 'registrations#new', as: :new_staff_registration, as_staff: true
    # register under company team admin
    get 'new-company-customer/:company_team_admin_code' => 'registrations#new', as: :new_company_customer_registration

    # register new company team admin
    get 'new-company-team-admin/:adminable_customer_code' => 'registrations#new', as: :new_customer_admin_registration

    # once the user registers, devise posts to register
    match 'register' => 'registrations#create', as: :register, via: :post

    # user changes password from protected area
    match 'change_password' => 'registrations#change_password', as: :change_password, via: %i[get put]

    # # password reset
    get   '/reset-password/change' => 'devise/passwords#edit'

    get 'sign_in_as/:user_id' => 'sessions#sign_in_as', as: :sign_in_as
    get 'sign_out_as' => 'sessions#sign_out_as', as: :sign_out_as
    get 'sign_in_as_admin/:user_id' => 'sessions#sign_in_as_admin', as: :sign_in_as_admin
    get 'sign_out_as_admin' => 'sessions#sign_out_as_admin', as: :sign_out_as_admin
    get 'sign_in_as_customer/:user_id' => 'sessions#sign_in_as_customer', as: :sign_in_as_customer
    get 'sign_out_as_customer' => 'sessions#sign_out_as_customer', as: :sign_out_as_customer
    get 'sign_in_as_supplier/:user_id' => 'sessions#sign_in_as_supplier', as: :sign_in_as_supplier

  end

  # for app page
  match '/p/:slug' => 'pages#show', as: :show_page, via: %i[get post]

  # # For form to submit Proof of Delivery and Invoice documents and to process it (Category Solutions only)
  # match 'document/:order_id/:profile_type/:profile_id/:hashed_value' => 'external_documents#submit_documents', as: :upload_documents, via: %i[get post put]

  # For short URLS. Do not match the /admin route which the name conficts with
  get '/s/:id' => 'shortener/shortened_urls#show', as: :shortened

  # # For saving additional details when rejecting a Category solutions order
  # match 'reject_order/:order_id/:profile_type/(:profile_id)' => 'orders#reject', as: :reject_order, via: %i[post put]

  # this engine should be always mounted at the application who is using this..
  mount RailsAdmin::Engine => '/admin-old', as: 'rails_admin'

  # this is catch-all page -> nothing goes below this
  get ':page', to: 'pages#show', as: :page, constraints: { page: /[a-zA-Z\-_\/]+/ }
  defaults subdomain: (yordar_credentials(:prismic_subdomain) || '') do
    get ':page', to: 'pages#show', as: :prismic_page, constraints: { page: /[a-zA-Z\-_\/]+/ }
  end

  match '/404', to: 'errors#not_found', via: :all
  match '/500', to: 'errors#internal_server_error', via: :all
end
