import { useState } from 'react';

import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';

import MealPlanCalendar from './calendar/MealPlanCalendar';
import MealPlanConfig from './meal_plan/MealPlanConfig';
import ExportPdfForm from 'components/customer/orders/ExportPdfForm';
import MealPlanCancelModal from './meal_plan/MealPlanCancelModal';

const MealPlanContainer = () => {
  const [activePanel, setActivePanel] = useState('calendar');
  const [exportPdf, setExportPdf] = useState(false);
  const [isRemove, setIsRemove] = useState(false);

  const { selectedMealPlan } = mealPlansStore(
    (state) => ({
      selectedMealPlan: state.selectedMealPlan,
    }),
    shallow
  );

  return (
    <>
      <div className="meal-plan-toolbar">
        <div className={activePanel === 'calendar' ? 'active' : ''} onClick={() => setActivePanel('calendar')}>
          Calendar
        </div>
        {selectedMealPlan && (
          <>
            <div className={activePanel === 'config' ? 'active' : ''} onClick={() => setActivePanel('config')}>
              Config
            </div>
            {selectedMealPlan.kind === 'shared' && (
              <div onClick={() => setExportPdf(true)}>
                Export PDF
              </div>
            )}
            <div className="is-invalid-label" onClick={() => setIsRemove(true)}>
              Remove
            </div>
          </>
        )}
      </div>
      <div className="meal-plan-container">
        {activePanel === 'calendar' && <MealPlanCalendar />}
        {activePanel === 'config' && <MealPlanConfig setActivePanel={setActivePanel} mealPlan={selectedMealPlan} />}
      </div>
      {isRemove && <MealPlanCancelModal mealPlan={selectedMealPlan} setIsRemove={setIsRemove} />}
      {!!exportPdf && <ExportPdfForm setExportPdf={setExportPdf} selectedMealPlan={selectedMealPlan} />}
    </>
  );
};

export default MealPlanContainer;
